<template>
  <nav class="bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100 sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center">
          <!-- Logo amélioré -->
          <router-link to="/" class="flex-shrink-0 flex items-center group">
            <div class="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-200">
              <span class="text-xl">🎬</span>
            </div>
            <span class="text-2xl font-black bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Flo Movie
            </span>
          </router-link>

          <!-- Navigation principale avec design moderne -->
          <div class="hidden md:ml-8 md:flex md:space-x-1">
            <router-link
              to="/"
              class="relative px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 group"
              :class="$route.path === '/'
                ? 'text-blue-600 bg-blue-50'
                : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'"
            >
              <span class="relative z-10">Accueil</span>
              <div v-if="$route.path === '/'" class="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl"></div>
            </router-link>
            <router-link
              to="/popular"
              class="relative px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 group"
              :class="$route.path === '/popular'
                ? 'text-blue-600 bg-blue-50'
                : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'"
            >
              <span class="relative z-10">Populaires</span>
              <div v-if="$route.path === '/popular'" class="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl"></div>
            </router-link>
            <router-link
              to="/top-rated"
              class="relative px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 group"
              :class="$route.path === '/top-rated'
                ? 'text-blue-600 bg-blue-50'
                : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'"
            >
              <span class="relative z-10">Les mieux notés</span>
              <div v-if="$route.path === '/top-rated'" class="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl"></div>
            </router-link>
            <router-link
              v-if="authStore.isAuthenticated"
              to="/favorites"
              class="relative px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 group"
              :class="$route.path === '/favorites'
                ? 'text-blue-600 bg-blue-50'
                : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'"
            >
              <span class="relative z-10 flex items-center">
                <HeartIcon class="h-4 w-4 mr-1" />
                Mes favoris
              </span>
              <div v-if="$route.path === '/favorites'" class="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl"></div>
            </router-link>
          </div>
        </div>

        <!-- Barre de recherche améliorée -->
        <div class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-end">
          <div class="max-w-lg w-full lg:max-w-sm">
            <label for="search" class="sr-only">Rechercher</label>
            <div class="relative group">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon class="h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors" />
              </div>
              <input
                id="search"
                v-model="searchQuery"
                name="search"
                class="block w-full pl-10 pr-3 py-2.5 border-2 border-gray-200 rounded-xl leading-5 bg-gray-50/50 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 focus:bg-white sm:text-sm transition-all duration-200"
                placeholder="Rechercher un film..."
                type="search"
                @keyup.enter="handleSearch"
              />
            </div>
          </div>
        </div>

        <!-- Menu utilisateur amélioré -->
        <div class="flex items-center space-x-3">
          <template v-if="authStore.isAuthenticated">
            <!-- Menu déroulant utilisateur -->
            <div class="relative" ref="userMenuRef">
              <button
                @click="showUserMenu = !showUserMenu"
                class="flex items-center space-x-3 text-sm rounded-xl p-2 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500/20 transition-all duration-200 group"
              >
                <div class="h-10 w-10 rounded-xl bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-200">
                  <span class="text-white font-bold text-lg">
                    {{ authStore.currentUser?.firstName?.charAt(0) || authStore.currentUser?.username?.charAt(0) || 'U' }}
                  </span>
                </div>
                <div class="hidden md:block text-left">
                  <div class="text-sm font-medium text-gray-900">
                    {{ authStore.currentUser?.firstName || authStore.currentUser?.username || 'Utilisateur' }}
                  </div>
                  <div class="text-xs text-gray-500">Mon compte</div>
                </div>
                <svg class="hidden md:block h-4 w-4 text-gray-400 group-hover:text-gray-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </button>
              
              <transition
                enter-active-class="transition ease-out duration-200"
                enter-from-class="transform opacity-0 scale-95"
                enter-to-class="transform opacity-100 scale-100"
                leave-active-class="transition ease-in duration-75"
                leave-from-class="transform opacity-100 scale-100"
                leave-to-class="transform opacity-0 scale-95"
              >
                <div
                  v-show="showUserMenu"
                  class="origin-top-right absolute right-0 mt-3 w-64 rounded-2xl shadow-xl bg-white ring-1 ring-gray-200 focus:outline-none z-50 border border-gray-100"
                >
                  <div class="p-2">
                    <div class="px-4 py-3 border-b border-gray-100">
                      <div class="flex items-center space-x-3">
                        <div class="h-12 w-12 rounded-xl bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center">
                          <span class="text-white font-bold">
                            {{ authStore.currentUser?.firstName?.charAt(0) || authStore.currentUser?.username?.charAt(0) || 'U' }}
                          </span>
                        </div>
                        <div>
                          <div class="font-semibold text-gray-900">
                            {{ authStore.currentUser?.firstName }} {{ authStore.currentUser?.lastName }}
                          </div>
                          <div class="text-sm text-gray-500">{{ authStore.currentUser?.email }}</div>
                        </div>
                      </div>
                    </div>

                    <div class="py-2">
                      <router-link
                        to="/favorites"
                        class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 rounded-xl transition-colors group"
                        @click="showUserMenu = false"
                      >
                        <HeartIcon class="h-5 w-5 mr-3 text-gray-400 group-hover:text-red-500" />
                        Mes favoris
                      </router-link>
                      <router-link
                        to="/downloads"
                        class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 rounded-xl transition-colors group"
                        @click="showUserMenu = false"
                      >
                        <svg class="h-5 w-5 mr-3 text-gray-400 group-hover:text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Mes téléchargements
                      </router-link>
                    </div>

                    <div class="border-t border-gray-100 pt-2">
                      <button
                        @click="handleLogout"
                        class="flex items-center w-full px-4 py-3 text-sm text-red-600 hover:bg-red-50 rounded-xl transition-colors group"
                      >
                        <svg class="h-5 w-5 mr-3 text-red-400 group-hover:text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                        Se déconnecter
                      </button>
                    </div>
                  </div>
                </div>
              </transition>
            </div>
          </template>

          <template v-else>
            <router-link
              to="/login"
              class="text-gray-700 hover:text-blue-600 px-4 py-2 rounded-xl text-sm font-medium transition-colors"
            >
              Connexion
            </router-link>
            <router-link
              to="/register"
              class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-2.5 rounded-xl text-sm font-medium transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              Inscription
            </router-link>
          </template>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { MagnifyingGlassIcon, HeartIcon } from '@heroicons/vue/24/outline'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const searchQuery = ref('')
const showUserMenu = ref(false)
const userMenuRef = ref(null)

const handleSearch = () => {
  if (searchQuery.value.trim()) {
    router.push(`/search?q=${encodeURIComponent(searchQuery.value.trim())}`)
  }
}

const handleLogout = () => {
  authStore.logout()
  showUserMenu.value = false
  router.push('/')
}

// Fermer le menu utilisateur en cliquant à l'extérieur
const handleClickOutside = (event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target)) {
    showUserMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
