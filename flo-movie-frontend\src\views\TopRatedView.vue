<template>
  <div class="container mx-auto px-4 py-8">
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">Films les mieux notés</h1>
      <p class="text-gray-600">Les films avec les meilleures notes de la communauté</p>
    </div>

    <MoviesGrid
      :movies="moviesStore.topRatedMovies"
      :is-loading="moviesStore.isLoading"
      :is-loading-more="isLoadingMore"
      :show-load-more="canLoadMore"
      :error="moviesStore.error"
      empty-message="Aucun film bien noté disponible"
      empty-description="Vérifiez votre connexion internet et réessayez."
      @load-more="loadMore"
      @retry="loadMovies"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useMoviesStore } from '@/stores/movies'
import MoviesGrid from '@/components/MoviesGrid.vue'

const moviesStore = useMoviesStore()

const currentPage = ref(1)
const isLoadingMore = ref(false)

const canLoadMore = computed(() => {
  return currentPage.value < moviesStore.totalPages && moviesStore.topRatedMovies.length > 0
})

const loadMovies = async (page = 1) => {
  try {
    currentPage.value = page
    await moviesStore.loadTopRatedMovies(page)
  } catch (error) {
    console.error('Erreur lors du chargement des films les mieux notés:', error)
  }
}

const loadMore = async () => {
  if (isLoadingMore.value || !canLoadMore.value) return
  
  isLoadingMore.value = true
  try {
    const nextPage = currentPage.value + 1
    await loadMovies(nextPage)
  } catch (error) {
    console.error('Erreur lors du chargement de plus de films:', error)
  } finally {
    isLoadingMore.value = false
  }
}

onMounted(() => {
  // Charger seulement si on n'a pas déjà des films les mieux notés
  if (moviesStore.topRatedMovies.length === 0) {
    loadMovies()
  }
})
</script>
