# Flo Movie Frontend

Interface utilisateur moderne pour l'application Flo Movie, construite avec Vue.js 3, Vite, et Tailwind CSS.

## 🚀 Fonctionnalités

- **🔐 Authentification** : Inscription, connexion, et gestion des sessions
- **🎬 Catalogue de films** : Navigation par catégories (populaires, mieux notés, au cinéma)
- **🔍 Recherche avancée** : Recherche de films avec historique
- **⭐ Favoris** : Système de favoris personnalisé
- **📥 Téléchargements** : Interface de gestion des téléchargements
- **📱 Design responsive** : Interface adaptée à tous les écrans
- **🎨 Interface moderne** : Design élégant avec Tailwind CSS

## 🛠️ Technologies utilisées

- **Vue.js 3** - Framework JavaScript progressif
- **Vite** - Outil de build rapide
- **Vue Router** - Routage côté client
- **Pinia** - Gestion d'état moderne pour Vue
- **Axios** - Client HTTP pour les requêtes API
- **Tailwind CSS** - Framework CSS utilitaire
- **Heroicons** - Icônes SVG

## 📦 Installation

1. **<PERSON><PERSON><PERSON> le projet** (si ce n'est pas déjà fait)
```bash
cd flo-movie-frontend
```

2. **Installer les dépendances**
```bash
npm install
```

3. **Configurer l'environnement**
```bash
# Le fichier .env est déjà configuré avec les bonnes valeurs par défaut
# Vérifiez que VITE_API_BASE_URL pointe vers votre API backend
```

4. **Démarrer le serveur de développement**
```bash
npm run dev
```

L'application sera accessible sur `http://localhost:5173`

## 🔧 Scripts disponibles

```bash
# Démarrer le serveur de développement
npm run dev

# Construire pour la production
npm run build

# Prévisualiser la build de production
npm run preview

# Linter le code
npm run lint
```

## 📁 Structure du projet

```
src/
├── components/          # Composants réutilisables
│   ├── LoginForm.vue   # Formulaire de connexion
│   ├── RegisterForm.vue # Formulaire d'inscription
│   ├── NavBar.vue      # Barre de navigation
│   ├── MovieCard.vue   # Carte de film
│   └── MoviesGrid.vue  # Grille de films
├── views/              # Pages/vues de l'application
│   ├── HomeView.vue    # Page d'accueil
│   ├── LoginView.vue   # Page de connexion
│   ├── RegisterView.vue # Page d'inscription
│   ├── PopularView.vue # Films populaires
│   ├── TopRatedView.vue # Films les mieux notés
│   └── SearchView.vue  # Page de recherche
├── stores/             # Stores Pinia
│   ├── auth.js         # Gestion de l'authentification
│   └── movies.js       # Gestion des films
├── services/           # Services API
│   ├── api.js          # Configuration Axios
│   ├── auth.js         # Services d'authentification
│   └── movies.js       # Services de films
├── router/             # Configuration du routeur
│   └── index.js        # Routes de l'application
└── assets/             # Assets statiques
    └── main.css        # Styles globaux avec Tailwind
```

## 🔗 Intégration avec l'API

L'application frontend communique avec l'API backend Flo Movie via :

- **Base URL** : `http://localhost:3000` (configurable via `.env`)
- **Authentification** : JWT tokens stockés dans localStorage
- **Intercepteurs Axios** : Gestion automatique des tokens et erreurs

### Endpoints utilisés :

- `POST /auth/register` - Inscription
- `POST /auth/login` - Connexion
- `GET /movies/search` - Recherche de films
- `GET /movies/popular` - Films populaires
- `GET /movies/top-rated` - Films les mieux notés
- `GET /movies/now-playing` - Films au cinéma
- `POST /movies/favorites` - Ajouter aux favoris
- `GET /movies/favorites/list` - Lister les favoris
- `DELETE /movies/favorites/:id` - Supprimer des favoris

## 🎨 Personnalisation

### Couleurs
Les couleurs sont configurées dans `tailwind.config.js` :
- **Primary** : Bleu (personnalisable)
- **Dark** : Nuances de gris pour le mode sombre

### Styles
Les styles personnalisés sont dans `src/assets/main.css` :
- Animations de chargement
- Effets de survol
- Scrollbar personnalisée

## 🚀 Déploiement

1. **Build de production**
```bash
npm run build
```

2. **Déployer le dossier `dist/`** sur votre serveur web

### Variables d'environnement pour la production :
```env
VITE_API_BASE_URL=https://votre-api.com
VITE_APP_TITLE=Flo Movie
```

## 🔧 Développement

### Ajouter une nouvelle page :
1. Créer le composant dans `src/views/`
2. Ajouter la route dans `src/router/index.js`
3. Ajouter la navigation dans `NavBar.vue`

### Ajouter un nouveau service API :
1. Créer les méthodes dans `src/services/`
2. Ajouter les actions dans le store Pinia approprié
3. Utiliser dans les composants

## 📝 Notes importantes

- **CORS** : L'API backend doit autoriser les requêtes depuis `http://localhost:5173`
- **Tokens JWT** : Stockés dans localStorage (considérer httpOnly cookies pour la production)
- **Images TMDB** : Utilise l'API TMDB pour les posters de films
- **Responsive** : Interface optimisée pour mobile, tablette et desktop

## 🐛 Dépannage

### Erreurs courantes :

1. **Erreur CORS** : Vérifiez que l'API backend autorise les requêtes depuis le frontend
2. **API non accessible** : Vérifiez que l'API backend fonctionne sur le port 3000
3. **Images non chargées** : Vérifiez la clé API TMDB dans le backend

### Logs de développement :
```bash
# Ouvrir les DevTools du navigateur (F12)
# Onglet Console pour voir les erreurs JavaScript
# Onglet Network pour voir les requêtes API
```

## 🎯 Prochaines étapes

- [ ] Mode sombre complet
- [ ] Notifications push
- [ ] Cache des données avec Service Workers
- [ ] Tests unitaires avec Vitest
- [ ] Internationalisation (i18n)
- [ ] Optimisation des performances
