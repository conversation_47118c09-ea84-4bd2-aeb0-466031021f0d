<template>
  <div class="movies-grid">
    <!-- Loading state -->
    <div v-if="isLoading && movies.length === 0" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
      <div v-for="n in 10" :key="n" class="animate-pulse">
        <div class="bg-gray-300 rounded-lg h-64 mb-4"></div>
        <div class="h-4 bg-gray-300 rounded mb-2"></div>
        <div class="h-3 bg-gray-300 rounded mb-2"></div>
        <div class="h-3 bg-gray-300 rounded w-3/4"></div>
      </div>
    </div>

    <!-- Movies grid -->
    <div v-else-if="movies.length > 0" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
      <MovieCard
        v-for="movie in movies"
        :key="movie.id"
        :movie="movie"
      />
    </div>

    <!-- Empty state -->
    <div v-else-if="!isLoading" class="text-center py-12">
      <div class="text-gray-400 text-6xl mb-4">🎬</div>
      <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ emptyMessage }}</h3>
      <p class="text-gray-600">{{ emptyDescription }}</p>
    </div>

    <!-- Load more button -->
    <div v-if="showLoadMore && !isLoading" class="text-center mt-8">
      <button
        @click="$emit('load-more')"
        class="btn-primary"
        :disabled="isLoadingMore"
      >
        <span v-if="isLoadingMore" class="flex items-center">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
          Chargement...
        </span>
        <span v-else>Charger plus</span>
      </button>
    </div>

    <!-- Loading more indicator -->
    <div v-if="isLoadingMore" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6 mt-6">
      <div v-for="n in 5" :key="`loading-${n}`" class="animate-pulse">
        <div class="bg-gray-300 rounded-lg h-64 mb-4"></div>
        <div class="h-4 bg-gray-300 rounded mb-2"></div>
        <div class="h-3 bg-gray-300 rounded mb-2"></div>
        <div class="h-3 bg-gray-300 rounded w-3/4"></div>
      </div>
    </div>

    <!-- Error state -->
    <div v-if="error" class="text-center py-12">
      <div class="text-red-400 text-6xl mb-4">⚠️</div>
      <h3 class="text-xl font-semibold text-gray-900 mb-2">Erreur de chargement</h3>
      <p class="text-gray-600 mb-4">{{ error }}</p>
      <button @click="$emit('retry')" class="btn-primary">
        Réessayer
      </button>
    </div>
  </div>
</template>

<script setup>
import MovieCard from './MovieCard.vue'

defineProps({
  movies: {
    type: Array,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  isLoadingMore: {
    type: Boolean,
    default: false
  },
  showLoadMore: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: null
  },
  emptyMessage: {
    type: String,
    default: 'Aucun film trouvé'
  },
  emptyDescription: {
    type: String,
    default: 'Essayez de modifier vos critères de recherche.'
  }
})

defineEmits(['load-more', 'retry'])
</script>
