import { 
  Controller, 
  Get, 
  Post, 
  Delete, 
  Query, 
  Param, 
  Body, 
  UseGuards, 
  Request,
  ValidationPipe,
  ParseIntPipe
} from '@nestjs/common';
import { MoviesService } from './movies.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { SearchMovieDto, AddFavoriteDto } from '../dto/movie.dto';

@Controller('movies')
export class MoviesController {
  constructor(private moviesService: MoviesService) {}

  @Get('search')
  async searchMovies(@Query(ValidationPipe) searchDto: SearchMovieDto) {
    return this.moviesService.searchMovies(searchDto.query, searchDto.page);
  }

  @Get('popular')
  async getPopularMovies(@Query('page', ParseIntPipe) page: number = 1) {
    return this.moviesService.getPopularMovies(page);
  }

  @Get('top-rated')
  async getTopRatedMovies(@Query('page', ParseIntPipe) page: number = 1) {
    return this.moviesService.getTopRatedMovies(page);
  }

  @Get('now-playing')
  async getNowPlayingMovies(@Query('page', ParseIntPipe) page: number = 1) {
    return this.moviesService.getNowPlayingMovies(page);
  }

  @Get(':id')
  async getMovieDetails(@Param('id', ParseIntPipe) tmdbId: number) {
    return this.moviesService.getMovieDetails(tmdbId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('favorites')
  async addToFavorites(
    @Request() req,
    @Body(ValidationPipe) addFavoriteDto: AddFavoriteDto
  ) {
    return this.moviesService.addToFavorites(req.user.id, addFavoriteDto);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('favorites/:tmdbId')
  async removeFromFavorites(
    @Request() req,
    @Param('tmdbId', ParseIntPipe) tmdbId: number
  ) {
    return this.moviesService.removeFromFavorites(req.user.id, tmdbId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('favorites/list')
  async getUserFavorites(@Request() req) {
    return this.moviesService.getUserFavorites(req.user.id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('favorites/check/:tmdbId')
  async checkIfInFavorites(
    @Request() req,
    @Param('tmdbId', ParseIntPipe) tmdbId: number
  ) {
    const isInFavorites = await this.moviesService.isMovieInFavorites(req.user.id, tmdbId);
    return { isInFavorites };
  }
}
