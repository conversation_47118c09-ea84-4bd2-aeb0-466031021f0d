### Test de l'API Flo Movie
### Assurez-vous que l'application est démarrée avec npm run start:dev

### 1. Test de base - Hello World
GET http://localhost:3000

### 2. Inscription d'un utilisateur
POST http://localhost:3000/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "username": "testuser",
  "password": "password123",
  "firstName": "<PERSON>",
  "lastName": "<PERSON><PERSON>"
}

### 3. Connexion
POST http://localhost:3000/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

### 4. Recherche de films (sans authentification)
GET http://localhost:3000/movies/search?query=avengers&page=1

### 5. Films populaires
GET http://localhost:3000/movies/popular?page=1

### 6. Films les mieux notés
GET http://localhost:3000/movies/top-rated?page=1

### 7. Films actuellement au cinéma
GET http://localhost:3000/movies/now-playing?page=1

### 8. Détails d'un film (exemple avec Avengers: Infinity War - ID TMDB: 299536)
GET http://localhost:3000/movies/299536

### 9. Ajouter un film aux favoris (nécessite authentification)
### Remplacez YOUR_JWT_TOKEN par le token obtenu lors de la connexion
POST http://localhost:3000/movies/favorites
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "tmdbId": 299536,
  "title": "Avengers: Infinity War",
  "overview": "As the Avengers and their allies have continued to protect the world from threats too large for any one hero to handle, a new danger has emerged from the cosmic shadows: Thanos.",
  "releaseDate": "2018-04-25",
  "posterPath": "/7WsyChQLEftFiDOVTGkv3hFpyyt.jpg",
  "voteAverage": 8.3,
  "voteCount": 16000
}

### 10. Lister les favoris
GET http://localhost:3000/movies/favorites/list
Authorization: Bearer YOUR_JWT_TOKEN

### 11. Vérifier si un film est en favoris
GET http://localhost:3000/movies/favorites/check/299536
Authorization: Bearer YOUR_JWT_TOKEN

### 12. Créer un téléchargement
POST http://localhost:3000/downloads
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "tmdbId": 299536,
  "title": "Avengers: Infinity War",
  "quality": "1080p",
  "downloadUrl": "https://example.com/movie.mp4",
  "fileName": "avengers_infinity_war_1080p.mp4",
  "fileSize": 2147483648
}

### 13. Lister les téléchargements
GET http://localhost:3000/downloads
Authorization: Bearer YOUR_JWT_TOKEN

### 14. Statistiques des téléchargements
GET http://localhost:3000/downloads/stats
Authorization: Bearer YOUR_JWT_TOKEN

### 15. Simuler un téléchargement (remplacez 1 par l'ID du téléchargement)
POST http://localhost:3000/downloads/1/simulate
Authorization: Bearer YOUR_JWT_TOKEN

### 16. Supprimer un film des favoris
DELETE http://localhost:3000/movies/favorites/299536
Authorization: Bearer YOUR_JWT_TOKEN

### 17. Annuler un téléchargement (remplacez 1 par l'ID du téléchargement)
DELETE http://localhost:3000/downloads/1
Authorization: Bearer YOUR_JWT_TOKEN
