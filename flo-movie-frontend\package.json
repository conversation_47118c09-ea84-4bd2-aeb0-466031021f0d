{"name": "flo-movie-frontend", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix"}, "dependencies": {"@heroicons/vue": "^2.2.0", "axios": "^1.12.2", "pinia": "^3.0.3", "vue": "^3.5.22", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.33.0", "@vitejs/plugin-vue": "^6.0.1", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-plugin-vue": "~10.4.0", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.18", "vite": "^7.1.7", "vite-plugin-vue-devtools": "^8.0.2"}}