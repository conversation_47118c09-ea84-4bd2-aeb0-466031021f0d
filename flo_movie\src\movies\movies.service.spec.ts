import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { NotFoundException } from '@nestjs/common';
import { MoviesService } from './movies.service';
import { TmdbService } from './tmdb.service';
import { Movie } from '../entities/movie.entity';
import { Favorite } from '../entities/favorite.entity';
import { User } from '../entities/user.entity';

describe('MoviesService', () => {
  let service: MoviesService;
  let mockMovieRepository: any;
  let mockFavoriteRepository: any;
  let mockUserRepository: any;
  let mockTmdbService: any;

  beforeEach(async () => {
    mockMovieRepository = {
      findOne: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
    };

    mockFavoriteRepository = {
      findOne: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
      remove: jest.fn(),
      find: jest.fn(),
    };

    mockUserRepository = {
      findOne: jest.fn(),
    };

    mockTmdbService = {
      searchMovies: jest.fn(),
      getMovieDetails: jest.fn(),
      getPopularMovies: jest.fn(),
      getTopRatedMovies: jest.fn(),
      getNowPlayingMovies: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MoviesService,
        {
          provide: getRepositoryToken(Movie),
          useValue: mockMovieRepository,
        },
        {
          provide: getRepositoryToken(Favorite),
          useValue: mockFavoriteRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: TmdbService,
          useValue: mockTmdbService,
        },
      ],
    }).compile();

    service = module.get<MoviesService>(MoviesService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('searchMovies', () => {
    it('should search movies using TMDB service', async () => {
      const query = 'avengers';
      const page = 1;
      const expectedResult = { results: [], total_pages: 1 };

      mockTmdbService.searchMovies.mockResolvedValue(expectedResult);

      const result = await service.searchMovies(query, page);

      expect(mockTmdbService.searchMovies).toHaveBeenCalledWith(query, page);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('addToFavorites', () => {
    it('should add a movie to favorites', async () => {
      const userId = 1;
      const addFavoriteDto = {
        tmdbId: 299536,
        title: 'Avengers: Infinity War',
        overview: 'Test overview',
        releaseDate: '2018-04-25',
        posterPath: '/test.jpg',
        voteAverage: 8.3,
        voteCount: 1000,
      };

      const user = { id: userId, email: '<EMAIL>' };
      const movie = { id: 1, tmdbId: addFavoriteDto.tmdbId, title: addFavoriteDto.title };
      const favorite = { id: 1, user, movie };

      mockUserRepository.findOne.mockResolvedValue(user);
      mockMovieRepository.findOne.mockResolvedValue(null);
      mockMovieRepository.create.mockReturnValue(movie);
      mockMovieRepository.save.mockResolvedValue(movie);
      mockFavoriteRepository.findOne.mockResolvedValue(null);
      mockFavoriteRepository.create.mockReturnValue(favorite);
      mockFavoriteRepository.save.mockResolvedValue(favorite);

      const result = await service.addToFavorites(userId, addFavoriteDto);

      expect(mockUserRepository.findOne).toHaveBeenCalledWith({ where: { id: userId } });
      expect(mockMovieRepository.create).toHaveBeenCalled();
      expect(mockFavoriteRepository.create).toHaveBeenCalledWith({ user, movie });
      expect(result).toEqual(favorite);
    });

    it('should throw NotFoundException if user not found', async () => {
      const userId = 999;
      const addFavoriteDto = {
        tmdbId: 299536,
        title: 'Avengers: Infinity War',
      };

      mockUserRepository.findOne.mockResolvedValue(null);

      await expect(service.addToFavorites(userId, addFavoriteDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('getUserFavorites', () => {
    it('should return user favorites', async () => {
      const userId = 1;
      const favorites = [
        { id: 1, movie: { title: 'Movie 1' } },
        { id: 2, movie: { title: 'Movie 2' } },
      ];

      mockFavoriteRepository.find.mockResolvedValue(favorites);

      const result = await service.getUserFavorites(userId);

      expect(mockFavoriteRepository.find).toHaveBeenCalledWith({
        where: { user: { id: userId } },
        relations: ['movie'],
        order: { createdAt: 'DESC' },
      });
      expect(result).toEqual(favorites);
    });
  });

  describe('isMovieInFavorites', () => {
    it('should return true if movie is in favorites', async () => {
      const userId = 1;
      const tmdbId = 299536;
      const movie = { id: 1, tmdbId };
      const favorite = { id: 1 };

      mockMovieRepository.findOne.mockResolvedValue(movie);
      mockFavoriteRepository.findOne.mockResolvedValue(favorite);

      const result = await service.isMovieInFavorites(userId, tmdbId);

      expect(result).toBe(true);
    });

    it('should return false if movie is not in favorites', async () => {
      const userId = 1;
      const tmdbId = 299536;

      mockMovieRepository.findOne.mockResolvedValue(null);

      const result = await service.isMovieInFavorites(userId, tmdbId);

      expect(result).toBe(false);
    });
  });
});
