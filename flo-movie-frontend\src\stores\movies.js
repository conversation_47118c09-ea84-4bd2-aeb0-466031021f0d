import { defineStore } from 'pinia'
import { moviesService } from '@/services/movies'

export const useMoviesStore = defineStore('movies', {
  state: () => ({
    // Listes de films
    popularMovies: [],
    topRatedMovies: [],
    nowPlayingMovies: [],
    searchResults: [],
    favorites: [],
    
    // Film actuellement sélectionné
    currentMovie: null,
    
    // États de chargement
    isLoading: false,
    isLoadingFavorites: false,
    isLoadingSearch: false,
    
    // Erreurs
    error: null,
    
    // Pagination
    currentPage: 1,
    totalPages: 1,
    
    // Recherche
    searchQuery: '',
    searchHistory: []
  }),

  getters: {
    // Vérifier si un film est en favoris
    isMovieInFavorites: (state) => (tmdbId) => {
      return state.favorites.some(fav => fav.movie.tmdbId === tmdbId)
    },
    
    // Obtenir l'URL de l'image
    getMovieImageUrl: () => (path, size = 'w500') => {
      return moviesService.getImageUrl(path, size)
    }
  },

  actions: {
    // Charger les films populaires
    async loadPopularMovies(page = 1) {
      this.isLoading = true
      this.error = null

      try {
        const response = await moviesService.getPopularMovies(page)
        if (page === 1) {
          this.popularMovies = response.results
        } else {
          this.popularMovies.push(...response.results)
        }
        this.currentPage = page
        this.totalPages = response.total_pages
      } catch (error) {
        this.error = error.message || 'Erreur lors du chargement des films populaires'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    // Charger les films les mieux notés
    async loadTopRatedMovies(page = 1) {
      this.isLoading = true
      this.error = null

      try {
        const response = await moviesService.getTopRatedMovies(page)
        if (page === 1) {
          this.topRatedMovies = response.results
        } else {
          this.topRatedMovies.push(...response.results)
        }
        this.currentPage = page
        this.totalPages = response.total_pages
      } catch (error) {
        this.error = error.message || 'Erreur lors du chargement des films les mieux notés'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    // Charger les films actuellement au cinéma
    async loadNowPlayingMovies(page = 1) {
      this.isLoading = true
      this.error = null

      try {
        const response = await moviesService.getNowPlayingMovies(page)
        if (page === 1) {
          this.nowPlayingMovies = response.results
        } else {
          this.nowPlayingMovies.push(...response.results)
        }
        this.currentPage = page
        this.totalPages = response.total_pages
      } catch (error) {
        this.error = error.message || 'Erreur lors du chargement des films au cinéma'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    // Rechercher des films
    async searchMovies(query, page = 1) {
      if (!query.trim()) return

      this.isLoadingSearch = true
      this.error = null
      this.searchQuery = query

      try {
        const response = await moviesService.searchMovies(query, page)
        if (page === 1) {
          this.searchResults = response.results
          // Ajouter à l'historique de recherche
          if (!this.searchHistory.includes(query)) {
            this.searchHistory.unshift(query)
            if (this.searchHistory.length > 10) {
              this.searchHistory = this.searchHistory.slice(0, 10)
            }
          }
        } else {
          this.searchResults.push(...response.results)
        }
        this.currentPage = page
        this.totalPages = response.total_pages
      } catch (error) {
        this.error = error.message || 'Erreur lors de la recherche'
        throw error
      } finally {
        this.isLoadingSearch = false
      }
    },

    // Charger les détails d'un film
    async loadMovieDetails(movieId) {
      this.isLoading = true
      this.error = null

      try {
        const movie = await moviesService.getMovieDetails(movieId)
        this.currentMovie = movie
        return movie
      } catch (error) {
        this.error = error.message || 'Erreur lors du chargement des détails du film'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    // Charger les favoris
    async loadFavorites() {
      this.isLoadingFavorites = true
      this.error = null

      try {
        const favorites = await moviesService.getFavorites()
        this.favorites = favorites
      } catch (error) {
        this.error = error.message || 'Erreur lors du chargement des favoris'
        throw error
      } finally {
        this.isLoadingFavorites = false
      }
    },

    // Ajouter aux favoris
    async addToFavorites(movieData) {
      try {
        await moviesService.addToFavorites(movieData)
        // Recharger les favoris
        await this.loadFavorites()
      } catch (error) {
        this.error = error.message || 'Erreur lors de l\'ajout aux favoris'
        throw error
      }
    },

    // Supprimer des favoris
    async removeFromFavorites(tmdbId) {
      try {
        await moviesService.removeFromFavorites(tmdbId)
        // Recharger les favoris
        await this.loadFavorites()
      } catch (error) {
        this.error = error.message || 'Erreur lors de la suppression des favoris'
        throw error
      }
    },

    // Effacer les erreurs
    clearError() {
      this.error = null
    },

    // Réinitialiser les résultats de recherche
    clearSearchResults() {
      this.searchResults = []
      this.searchQuery = ''
    }
  }
})
