import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MoviesController } from './movies.controller';
import { MoviesService } from './movies.service';
import { TmdbService } from './tmdb.service';
import { Movie } from '../entities/movie.entity';
import { Favorite } from '../entities/favorite.entity';
import { User } from '../entities/user.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Movie, Favorite, User])],
  controllers: [MoviesController],
  providers: [MoviesService, TmdbService],
  exports: [MoviesService, TmdbService],
})
export class MoviesModule {}
