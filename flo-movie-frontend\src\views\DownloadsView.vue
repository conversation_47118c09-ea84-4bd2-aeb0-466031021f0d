<template>
  <div class="container mx-auto px-4 py-8">
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">Mes téléchargements</h1>
      <p class="text-gray-600"><PERSON><PERSON><PERSON> vos téléchargements de films</p>
    </div>

    <!-- Coming soon message -->
    <div class="text-center py-12">
      <div class="text-primary-600 text-6xl mb-4">📥</div>
      <h3 class="text-xl font-semibold text-gray-900 mb-2">Fonctionnalité en développement</h3>
      <p class="text-gray-600 mb-6">
        La gestion des téléchargements sera bientôt disponible. 
        En attendant, vous pouvez explorer notre catalogue de films.
      </p>
      <div class="flex justify-center gap-4">
        <router-link
          to="/popular"
          class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
        >
          Films populaires
        </router-link>
        <router-link
          to="/favorites"
          class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-6 py-3 rounded-lg font-medium transition-colors"
        >
          Mes favoris
        </router-link>
      </div>
    </div>

    <!-- Future download interface mockup -->
    <div class="bg-gray-50 rounded-lg p-6 mt-8">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Aperçu des fonctionnalités à venir :</h3>
      <ul class="space-y-2 text-gray-600">
        <li class="flex items-center">
          <span class="text-green-500 mr-2">✓</span>
          Téléchargement de films en différentes qualités (720p, 1080p, 4K)
        </li>
        <li class="flex items-center">
          <span class="text-green-500 mr-2">✓</span>
          Suivi de progression en temps réel
        </li>
        <li class="flex items-center">
          <span class="text-green-500 mr-2">✓</span>
          Gestion de la file d'attente de téléchargement
        </li>
        <li class="flex items-center">
          <span class="text-green-500 mr-2">✓</span>
          Historique des téléchargements
        </li>
        <li class="flex items-center">
          <span class="text-green-500 mr-2">✓</span>
          Notifications de fin de téléchargement
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
// Cette vue sera développée plus tard avec la fonctionnalité complète de téléchargement
</script>
