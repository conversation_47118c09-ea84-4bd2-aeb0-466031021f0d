import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Download, DownloadStatus } from '../entities/download.entity';
import { Movie } from '../entities/movie.entity';
import { User } from '../entities/user.entity';
import { CreateDownloadDto, UpdateDownloadProgressDto } from '../dto/download.dto';

@Injectable()
export class DownloadsService {
  constructor(
    @InjectRepository(Download)
    private downloadRepository: Repository<Download>,
    @InjectRepository(Movie)
    private movieRepository: Repository<Movie>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async createDownload(userId: number, createDownloadDto: CreateDownloadDto): Promise<Download> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('Utilisateur non trouvé');
    }

    // Vérifier si le film existe dans notre base
    let movie = await this.movieRepository.findOne({ where: { tmdbId: createDownloadDto.tmdbId } });
    
    // Si le film n'existe pas, le créer avec les informations de base
    if (!movie) {
      movie = this.movieRepository.create({
        tmdbId: createDownloadDto.tmdbId,
        title: createDownloadDto.title,
      });
      movie = await this.movieRepository.save(movie);
    }

    // Vérifier s'il n'y a pas déjà un téléchargement en cours pour ce film
    const existingDownload = await this.downloadRepository.findOne({
      where: {
        user: { id: userId },
        movie: { id: movie.id },
        status: DownloadStatus.IN_PROGRESS,
      },
    });

    if (existingDownload) {
      throw new BadRequestException('Un téléchargement est déjà en cours pour ce film');
    }

    const download = this.downloadRepository.create({
      user,
      movie,
      quality: createDownloadDto.quality,
      downloadUrl: createDownloadDto.downloadUrl,
      fileName: createDownloadDto.fileName,
      fileSize: createDownloadDto.fileSize,
      status: DownloadStatus.PENDING,
    });

    return this.downloadRepository.save(download);
  }

  async getUserDownloads(userId: number): Promise<Download[]> {
    return this.downloadRepository.find({
      where: { user: { id: userId } },
      relations: ['movie'],
      order: { createdAt: 'DESC' },
    });
  }

  async getDownloadById(downloadId: number, userId: number): Promise<Download> {
    const download = await this.downloadRepository.findOne({
      where: { id: downloadId, user: { id: userId } },
      relations: ['movie'],
    });

    if (!download) {
      throw new NotFoundException('Téléchargement non trouvé');
    }

    return download;
  }

  async updateDownloadProgress(
    downloadId: number, 
    userId: number, 
    updateDto: UpdateDownloadProgressDto
  ): Promise<Download> {
    const download = await this.getDownloadById(downloadId, userId);

    download.progress = updateDto.progress;
    
    if (updateDto.errorMessage) {
      download.errorMessage = updateDto.errorMessage;
      download.status = DownloadStatus.FAILED;
    } else if (updateDto.progress >= 100) {
      download.status = DownloadStatus.COMPLETED;
      download.completedAt = new Date();
    } else if (updateDto.progress > 0) {
      download.status = DownloadStatus.IN_PROGRESS;
    }

    return this.downloadRepository.save(download);
  }

  async startDownload(downloadId: number, userId: number): Promise<Download> {
    const download = await this.getDownloadById(downloadId, userId);

    if (download.status !== DownloadStatus.PENDING) {
      throw new BadRequestException('Ce téléchargement ne peut pas être démarré');
    }

    download.status = DownloadStatus.IN_PROGRESS;
    return this.downloadRepository.save(download);
  }

  async cancelDownload(downloadId: number, userId: number): Promise<void> {
    const download = await this.getDownloadById(downloadId, userId);

    if (download.status === DownloadStatus.COMPLETED) {
      throw new BadRequestException('Impossible d\'annuler un téléchargement terminé');
    }

    await this.downloadRepository.remove(download);
  }

  async getDownloadStats(userId: number): Promise<any> {
    const downloads = await this.downloadRepository.find({
      where: { user: { id: userId } },
    });

    const stats = {
      total: downloads.length,
      completed: downloads.filter(d => d.status === DownloadStatus.COMPLETED).length,
      inProgress: downloads.filter(d => d.status === DownloadStatus.IN_PROGRESS).length,
      failed: downloads.filter(d => d.status === DownloadStatus.FAILED).length,
      pending: downloads.filter(d => d.status === DownloadStatus.PENDING).length,
    };

    return stats;
  }

  // Méthode pour simuler le téléchargement (à remplacer par une vraie logique de téléchargement)
  async simulateDownload(downloadId: number, userId: number): Promise<void> {
    const download = await this.startDownload(downloadId, userId);
    
    // Simulation du téléchargement avec des mises à jour de progression
    const intervals = [10, 25, 50, 75, 90, 100];
    
    for (const progress of intervals) {
      await new Promise(resolve => setTimeout(resolve, 1000)); // Attendre 1 seconde
      await this.updateDownloadProgress(downloadId, userId, { progress });
    }
  }
}
