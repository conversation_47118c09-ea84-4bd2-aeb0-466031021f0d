import api from './api'

export const moviesService = {
  // Rechercher des films
  async searchMovies(query, page = 1) {
    try {
      const response = await api.get(`/movies/search?query=${encodeURIComponent(query)}&page=${page}`)
      return response.data
    } catch (error) {
      throw error.response?.data || error.message
    }
  },

  // Films populaires
  async getPopularMovies(page = 1) {
    try {
      const response = await api.get(`/movies/popular?page=${page}`)
      return response.data
    } catch (error) {
      throw error.response?.data || error.message
    }
  },

  // Films les mieux notés
  async getTopRatedMovies(page = 1) {
    try {
      const response = await api.get(`/movies/top-rated?page=${page}`)
      return response.data
    } catch (error) {
      throw error.response?.data || error.message
    }
  },

  // Films actuellement au cinéma
  async getNowPlayingMovies(page = 1) {
    try {
      const response = await api.get(`/movies/now-playing?page=${page}`)
      return response.data
    } catch (error) {
      throw error.response?.data || error.message
    }
  },

  // Détails d'un film
  async getMovieDetails(movieId) {
    try {
      const response = await api.get(`/movies/${movieId}`)
      return response.data
    } catch (error) {
      throw error.response?.data || error.message
    }
  },

  // Ajouter aux favoris
  async addToFavorites(movieData) {
    try {
      const response = await api.post('/movies/favorites', movieData)
      return response.data
    } catch (error) {
      throw error.response?.data || error.message
    }
  },

  // Supprimer des favoris
  async removeFromFavorites(tmdbId) {
    try {
      const response = await api.delete(`/movies/favorites/${tmdbId}`)
      return response.data
    } catch (error) {
      throw error.response?.data || error.message
    }
  },

  // Lister les favoris
  async getFavorites() {
    try {
      const response = await api.get('/movies/favorites/list')
      return response.data
    } catch (error) {
      throw error.response?.data || error.message
    }
  },

  // Vérifier si un film est en favoris
  async isInFavorites(tmdbId) {
    try {
      const response = await api.get(`/movies/favorites/check/${tmdbId}`)
      return response.data.isInFavorites
    } catch (error) {
      throw error.response?.data || error.message
    }
  },

  // Utilitaire pour construire l'URL des images TMDB
  getImageUrl(path, size = 'w500') {
    if (!path) return '/placeholder-movie.jpg'
    return `https://image.tmdb.org/t/p/${size}${path}`
  }
}
