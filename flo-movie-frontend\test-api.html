<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Flo Movie</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        .error {
            background: #ffebee;
            color: #c62828;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1565c0;
        }
    </style>
</head>
<body>
    <h1>🎬 Test API Flo Movie</h1>
    
    <div class="test-section">
        <h2>Test de connexion au backend</h2>
        <button onclick="testConnection()">Tester la connexion</button>
        <div id="connection-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test des films populaires</h2>
        <button onclick="testPopularMovies()">Charger les films populaires</button>
        <div id="popular-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test d'inscription</h2>
        <button onclick="testRegister()">Tester l'inscription</button>
        <div id="register-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test de connexion utilisateur</h2>
        <button onclick="testLogin()">Tester la connexion</button>
        <div id="login-result" class="result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3000';

        async function testConnection() {
            const resultDiv = document.getElementById('connection-result');
            resultDiv.textContent = 'Test en cours...';
            
            try {
                const response = await fetch(`${API_BASE_URL}/`);
                const data = await response.text();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Connexion réussie!\nRéponse: ${data}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Erreur ${response.status}: ${data}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Erreur de connexion: ${error.message}`;
            }
        }

        async function testPopularMovies() {
            const resultDiv = document.getElementById('popular-result');
            resultDiv.textContent = 'Chargement des films...';
            
            try {
                const response = await fetch(`${API_BASE_URL}/movies/popular?page=1`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Films chargés avec succès!\n${data.results.length} films trouvés\nPremier film: ${data.results[0]?.title || 'Aucun'}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Erreur ${response.status}: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Erreur: ${error.message}`;
            }
        }

        async function testRegister() {
            const resultDiv = document.getElementById('register-result');
            resultDiv.textContent = 'Test d\'inscription...';
            
            const userData = {
                email: `test${Date.now()}@example.com`,
                username: `testuser${Date.now()}`,
                password: 'password123',
                firstName: 'Test',
                lastName: 'User'
            };
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(userData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Inscription réussie!\nUtilisateur: ${data.user?.email}\nToken reçu: ${data.access_token ? 'Oui' : 'Non'}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Erreur ${response.status}: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Erreur: ${error.message}`;
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.textContent = 'Test de connexion...';
            
            const loginData = {
                email: '<EMAIL>',
                password: 'password123'
            };
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(loginData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Connexion réussie!\nUtilisateur: ${data.user?.email}\nToken reçu: ${data.access_token ? 'Oui' : 'Non'}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Erreur ${response.status}: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Erreur: ${error.message}`;
            }
        }

        // Test automatique au chargement
        window.onload = function() {
            console.log('🎬 Page de test chargée');
            console.log('API Base URL:', API_BASE_URL);
        };
    </script>
</body>
</html>
