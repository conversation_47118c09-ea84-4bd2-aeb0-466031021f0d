import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { Favorite } from './favorite.entity';

@Entity('movies')
export class Movie {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  tmdbId: number; // ID depuis l'API TMDB

  @Column()
  title: string;

  @Column({ type: 'text', nullable: true })
  overview?: string;

  @Column({ nullable: true })
  releaseDate?: string;

  @Column({ nullable: true })
  posterPath?: string;

  @Column({ nullable: true })
  backdropPath?: string;

  @Column({ type: 'decimal', precision: 3, scale: 1, nullable: true })
  voteAverage?: number;

  @Column({ nullable: true })
  voteCount?: number;

  @Column({ type: 'text', nullable: true })
  genres?: string; // JSON string des genres

  @Column({ nullable: true })
  runtime?: number;

  @Column({ type: 'text', nullable: true })
  downloadLinks?: string; // JSON string des liens de téléchargement

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => Favorite, favorite => favorite.movie)
  favorites: Favorite[];
}
