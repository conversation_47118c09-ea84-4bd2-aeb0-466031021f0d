import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Request,
  UseGuards,
  ValidationPipe,
  ParseIntPipe,
} from '@nestjs/common';
import { DownloadsService } from './downloads.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { CreateDownloadDto, UpdateDownloadProgressDto } from '../dto/download.dto';

@Controller('downloads')
@UseGuards(JwtAuthGuard)
export class DownloadsController {
  constructor(private downloadsService: DownloadsService) {}

  @Post()
  async createDownload(
    @Request() req,
    @Body(ValidationPipe) createDownloadDto: CreateDownloadDto,
  ) {
    return this.downloadsService.createDownload(req.user.id, createDownloadDto);
  }

  @Get()
  async getUserDownloads(@Request() req) {
    return this.downloadsService.getUserDownloads(req.user.id);
  }

  @Get('stats')
  async getDownloadStats(@Request() req) {
    return this.downloadsService.getDownloadStats(req.user.id);
  }

  @Get(':id')
  async getDownloadById(
    @Request() req,
    @Param('id', ParseIntPipe) downloadId: number,
  ) {
    return this.downloadsService.getDownloadById(downloadId, req.user.id);
  }

  @Put(':id/progress')
  async updateDownloadProgress(
    @Request() req,
    @Param('id', ParseIntPipe) downloadId: number,
    @Body(ValidationPipe) updateDto: UpdateDownloadProgressDto,
  ) {
    return this.downloadsService.updateDownloadProgress(downloadId, req.user.id, updateDto);
  }

  @Post(':id/start')
  async startDownload(
    @Request() req,
    @Param('id', ParseIntPipe) downloadId: number,
  ) {
    return this.downloadsService.startDownload(downloadId, req.user.id);
  }

  @Post(':id/simulate')
  async simulateDownload(
    @Request() req,
    @Param('id', ParseIntPipe) downloadId: number,
  ) {
    // Démarrer la simulation en arrière-plan
    this.downloadsService.simulateDownload(downloadId, req.user.id);
    return { message: 'Simulation de téléchargement démarrée' };
  }

  @Delete(':id')
  async cancelDownload(
    @Request() req,
    @Param('id', ParseIntPipe) downloadId: number,
  ) {
    await this.downloadsService.cancelDownload(downloadId, req.user.id);
    return { message: 'Téléchargement annulé' };
  }
}
