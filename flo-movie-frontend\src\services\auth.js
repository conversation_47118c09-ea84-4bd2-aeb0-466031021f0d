import api from './api'

export const authService = {
  // Inscription
  async register(userData) {
    try {
      const response = await api.post('/auth/register', userData)
      return response.data
    } catch (error) {
      throw error.response?.data || error.message
    }
  },

  // Connexion
  async login(credentials) {
    try {
      const response = await api.post('/auth/login', credentials)
      const { user, token } = response.data
      
      // Stocker le token et les infos utilisateur
      localStorage.setItem('token', token)
      localStorage.setItem('user', JSON.stringify(user))
      
      return response.data
    } catch (error) {
      throw error.response?.data || error.message
    }
  },

  // Déconnexion
  logout() {
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  },

  // Vérifier si l'utilisateur est connecté
  isAuthenticated() {
    return !!localStorage.getItem('token')
  },

  // Récupérer l'utilisateur actuel
  getCurrentUser() {
    const user = localStorage.getItem('user')
    return user ? JSON.parse(user) : null
  },

  // Récupérer le token
  getToken() {
    return localStorage.getItem('token')
  }
}
