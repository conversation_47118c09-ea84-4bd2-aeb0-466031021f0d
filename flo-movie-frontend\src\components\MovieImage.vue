<template>
  <div class="relative overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200">
    <img
      v-if="!imageError && imageUrl"
      :src="imageUrl"
      :alt="alt"
      :class="imageClass"
      @load="onImageLoad"
      @error="onImageError"
      class="transition-opacity duration-300"
      :style="{ opacity: imageLoaded ? 1 : 0 }"
    />
    
    <!-- Loading placeholder -->
    <div
      v-if="!imageLoaded && !imageError"
      :class="placeholderClass"
      class="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-gray-200 to-gray-300 animate-pulse"
    >
      <div class="text-center text-gray-400">
        <FilmIcon class="h-12 w-12 mx-auto mb-2 opacity-50" />
        <div class="text-xs font-medium">Chargement...</div>
      </div>
    </div>
    
    <!-- Error placeholder with movie-themed design -->
    <div
      v-if="imageError"
      :class="placeholderClass"
      class="absolute inset-0 flex items-center justify-center text-white relative overflow-hidden"
      :style="{ background: gradientBackground }"
    >
      <!-- Animated background elements -->
      <div class="absolute inset-0">
        <div class="absolute top-0 left-0 w-full h-full opacity-10">
          <div class="absolute top-4 left-4 w-8 h-8 bg-white rounded-full animate-pulse"></div>
          <div class="absolute top-12 right-8 w-4 h-4 bg-white rounded-full animate-pulse" style="animation-delay: 0.5s"></div>
          <div class="absolute bottom-8 left-12 w-6 h-6 bg-white rounded-full animate-pulse" style="animation-delay: 1s"></div>
          <div class="absolute bottom-4 right-4 w-3 h-3 bg-white rounded-full animate-pulse" style="animation-delay: 1.5s"></div>
        </div>
      </div>
      
      <div class="text-center p-6 relative z-10">
        <!-- Movie camera icon with better design -->
        <div class="relative mb-4">
          <div class="w-20 h-16 mx-auto bg-gradient-to-r from-white/20 to-white/30 rounded-2xl flex items-center justify-center shadow-2xl backdrop-blur-sm border border-white/20">
            <FilmIcon class="h-10 w-10 text-white" />
          </div>
          <!-- Film strip decoration -->
          <div class="absolute -top-2 -left-2 w-6 h-3 bg-white/30 rounded-sm"></div>
          <div class="absolute -top-2 -right-2 w-6 h-3 bg-white/30 rounded-sm"></div>
          <div class="absolute -bottom-2 -left-2 w-6 h-3 bg-white/30 rounded-sm"></div>
          <div class="absolute -bottom-2 -right-2 w-6 h-3 bg-white/30 rounded-sm"></div>
        </div>
        
        <!-- Title with better typography -->
        <div class="text-lg font-bold text-white mb-2 line-clamp-2 px-2">
          {{ title || 'Film sans titre' }}
        </div>
        <div class="text-sm text-white/80 font-medium">Affiche non disponible</div>
        
        <!-- Genre indicator (mock) -->
        <div class="mt-3">
          <span class="bg-white/20 backdrop-blur-sm text-white px-3 py-1 rounded-full text-xs font-medium border border-white/30">
            {{ getRandomGenre() }}
          </span>
        </div>
      </div>
      
      <!-- Corner decorations -->
      <div class="absolute top-0 left-0 w-16 h-16 border-l-4 border-t-4 border-white/20 rounded-tl-lg"></div>
      <div class="absolute top-0 right-0 w-16 h-16 border-r-4 border-t-4 border-white/20 rounded-tr-lg"></div>
      <div class="absolute bottom-0 left-0 w-16 h-16 border-l-4 border-b-4 border-white/20 rounded-bl-lg"></div>
      <div class="absolute bottom-0 right-0 w-16 h-16 border-r-4 border-b-4 border-white/20 rounded-br-lg"></div>
    </div>
    
    <!-- Overlay gradient for better text readability -->
    <div
      v-if="showOverlay"
      class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent pointer-events-none"
    ></div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { FilmIcon } from '@heroicons/vue/24/outline'

const props = defineProps({
  src: {
    type: String,
    default: null
  },
  alt: {
    type: String,
    default: 'Image de film'
  },
  title: {
    type: String,
    default: null
  },
  imageClass: {
    type: String,
    default: 'w-full h-full object-cover'
  },
  placeholderClass: {
    type: String,
    default: 'w-full h-full'
  },
  showOverlay: {
    type: Boolean,
    default: false
  }
})

const imageError = ref(false)
const imageLoaded = ref(false)

// Gradients colorés basés sur le titre
const gradients = [
  'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
  'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
  'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
  'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
  'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
  'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
  'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
  'linear-gradient(135deg, #ff8a80 0%, #ea80fc 100%)',
  'linear-gradient(135deg, #8fd3f4 0%, #84fab0 100%)'
]

const genres = ['Action', 'Comédie', 'Drame', 'Thriller', 'Romance', 'Sci-Fi', 'Horreur', 'Aventure']

const imageUrl = computed(() => {
  if (!props.src) return null
  
  // Si c'est déjà une URL complète, l'utiliser
  if (props.src.startsWith('http')) {
    return props.src
  }
  
  // Sinon, construire l'URL TMDB
  return `https://image.tmdb.org/t/p/w500${props.src}`
})

const gradientBackground = computed(() => {
  if (!props.title) return gradients[0]
  
  // Utiliser le titre pour générer un index cohérent
  const hash = props.title.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0)
    return a & a
  }, 0)
  
  return gradients[Math.abs(hash) % gradients.length]
})

const getRandomGenre = () => {
  if (!props.title) return genres[0]
  
  // Utiliser le titre pour générer un genre cohérent
  const hash = props.title.split('').reduce((a, b) => {
    a = ((a << 3) - a) + b.charCodeAt(0)
    return a & a
  }, 0)
  
  return genres[Math.abs(hash) % genres.length]
}

const onImageLoad = () => {
  imageLoaded.value = true
  imageError.value = false
}

const onImageError = () => {
  imageError.value = true
  imageLoaded.value = false
}
</script>
