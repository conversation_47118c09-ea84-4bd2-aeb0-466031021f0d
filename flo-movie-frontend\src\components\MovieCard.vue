<template>
  <div class="movie-card group bg-white rounded-xl shadow-lg hover:shadow-2xl overflow-hidden cursor-pointer transform transition-all duration-300 hover:-translate-y-2" @click="goToMovie">
    <div class="relative h-80">
      <img
        :src="posterUrl"
        :alt="movie.title"
        class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
        @error="handleImageError"
      />

      <!-- Favorite button -->
      <div class="absolute top-3 right-3 z-10">
        <button
          v-if="authStore.isAuthenticated"
          @click.stop="toggleFavorite"
          class="p-2.5 rounded-full backdrop-blur-sm transition-all duration-200 transform hover:scale-110"
          :class="[
            isToggling ? 'opacity-50 cursor-not-allowed' : 'hover:bg-white/20',
            isInFavorites
              ? 'bg-red-500/90 text-white shadow-lg'
              : 'bg-black/40 text-white hover:bg-black/60'
          ]"
          :disabled="isToggling"
        >
          <HeartIcon
            :class="[
              'h-5 w-5 transition-all duration-200',
              isInFavorites ? 'fill-current scale-110' : ''
            ]"
          />
        </button>
      </div>

      <!-- Rating badge -->
      <div class="absolute bottom-3 left-3 z-10">
        <div class="flex items-center bg-black/70 backdrop-blur-sm text-white px-3 py-1.5 rounded-full text-sm font-medium">
          <span class="text-yellow-400 mr-1">⭐</span>
          {{ movie.vote_average?.toFixed(1) || 'N/A' }}
        </div>
      </div>

      <!-- Quality badge (mock) -->
      <div class="absolute top-3 left-3 z-10">
        <span class="bg-green-500/90 text-white px-2 py-1 rounded text-xs font-bold uppercase tracking-wide">
          HD
        </span>
      </div>

      <!-- Hover overlay with quick actions -->
      <div class="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center z-20">
        <div class="text-center text-white transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300">
          <button class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-full font-medium mb-2 transition-colors">
            Voir détails
          </button>
          <p class="text-sm opacity-90">Cliquez pour plus d'infos</p>
        </div>
      </div>
    </div>

    <div class="p-5">
      <h3 class="font-bold text-lg text-gray-900 mb-2 line-clamp-2 group-hover:text-primary-600 transition-colors">
        {{ movie.title }}
      </h3>
      <div class="flex items-center justify-between mb-3">
        <p class="text-gray-600 text-sm font-medium">
          {{ formatDate(movie.release_date) }}
        </p>
        <div class="flex items-center text-xs text-gray-500">
          <span class="mr-1">👥</span>
          {{ movie.vote_count || 0 }} votes
        </div>
      </div>
      <p class="text-gray-700 text-sm line-clamp-3 leading-relaxed">
        {{ movie.overview || 'Aucun synopsis disponible.' }}
      </p>

      <!-- Genres (if available) -->
      <div v-if="movie.genre_ids && movie.genre_ids.length > 0" class="mt-3 flex flex-wrap gap-1">
        <span
          v-for="genreId in movie.genre_ids.slice(0, 2)"
          :key="genreId"
          class="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs font-medium"
        >
          {{ getGenreName(genreId) }}
        </span>
        <span
          v-if="movie.genre_ids.length > 2"
          class="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs font-medium"
        >
          +{{ movie.genre_ids.length - 2 }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { HeartIcon } from '@heroicons/vue/24/outline'
import { useAuthStore } from '@/stores/auth'
import { useMoviesStore } from '@/stores/movies'

const props = defineProps({
  movie: {
    type: Object,
    required: true
  }
})

const router = useRouter()
const authStore = useAuthStore()
const moviesStore = useMoviesStore()

const isToggling = ref(false)
const imageError = ref(false)

// Mapping des genres TMDB
const genreMap = {
  28: 'Action',
  12: 'Aventure',
  16: 'Animation',
  35: 'Comédie',
  80: 'Crime',
  99: 'Documentaire',
  18: 'Drame',
  10751: 'Familial',
  14: 'Fantastique',
  36: 'Histoire',
  27: 'Horreur',
  10402: 'Musique',
  9648: 'Mystère',
  10749: 'Romance',
  878: 'Science-Fiction',
  10770: 'Téléfilm',
  53: 'Thriller',
  10752: 'Guerre',
  37: 'Western'
}

const posterUrl = computed(() => {
  if (imageError.value || !props.movie.poster_path) {
    return 'https://via.placeholder.com/300x450/374151/ffffff?text=No+Image'
  }
  return moviesStore.getMovieImageUrl(props.movie.poster_path)
})

const isInFavorites = computed(() => {
  return moviesStore.isMovieInFavorites(props.movie.id)
})

const goToMovie = () => {
  router.push(`/movie/${props.movie.id}`)
}

const formatDate = (dateString) => {
  if (!dateString) return 'Date inconnue'
  const date = new Date(dateString)
  return date.toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long'
  })
}

const getGenreName = (genreId) => {
  return genreMap[genreId] || 'Autre'
}

const handleImageError = () => {
  imageError.value = true
}

const toggleFavorite = async () => {
  if (isToggling.value) return

  isToggling.value = true

  try {
    const movieData = {
      tmdbId: props.movie.id,
      title: props.movie.title,
      overview: props.movie.overview,
      releaseDate: props.movie.release_date,
      posterPath: props.movie.poster_path,
      backdropPath: props.movie.backdrop_path,
      voteAverage: props.movie.vote_average,
      voteCount: props.movie.vote_count,
      genres: props.movie.genres || props.movie.genre_ids
    }

    if (isInFavorites.value) {
      await moviesStore.removeFromFavorites(props.movie.id)
    } else {
      await moviesStore.addToFavorites(movieData)
    }
  } catch (error) {
    console.error('Erreur lors de la gestion des favoris:', error)
  } finally {
    isToggling.value = false
  }
}

onMounted(() => {
  // Charger les favoris si l'utilisateur est connecté
  if (authStore.isAuthenticated) {
    moviesStore.loadFavorites()
  }
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
