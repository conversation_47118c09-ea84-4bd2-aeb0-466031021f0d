<template>
  <div class="container mx-auto px-4 py-8">
    <!-- Search Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-4">Recherche de films</h1>
      
      <!-- Search Bar -->
      <div class="max-w-2xl">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon class="h-5 w-5 text-gray-400" />
          </div>
          <input
            v-model="searchQuery"
            type="text"
            class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            placeholder="Rechercher un film..."
            @keyup.enter="handleSearch"
          />
        </div>
      </div>

      <!-- Search History -->
      <div v-if="moviesStore.searchHistory.length > 0 && !currentQuery" class="mt-4">
        <h3 class="text-sm font-medium text-gray-700 mb-2">Recherches récentes :</h3>
        <div class="flex flex-wrap gap-2">
          <button
            v-for="query in moviesStore.searchHistory"
            :key="query"
            @click="searchFromHistory(query)"
            class="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full text-sm transition-colors"
          >
            {{ query }}
          </button>
        </div>
      </div>
    </div>

    <!-- Search Results -->
    <div v-if="currentQuery">
      <div class="mb-6">
        <h2 class="text-xl font-semibold text-gray-900">
          Résultats pour "{{ currentQuery }}"
          <span v-if="moviesStore.searchResults.length > 0" class="text-gray-500 font-normal">
            ({{ moviesStore.searchResults.length }} résultats)
          </span>
        </h2>
      </div>

      <MoviesGrid
        :movies="moviesStore.searchResults"
        :is-loading="moviesStore.isLoadingSearch"
        :is-loading-more="isLoadingMore"
        :show-load-more="canLoadMore"
        :error="moviesStore.error"
        empty-message="Aucun film trouvé"
        :empty-description="`Aucun résultat pour '${currentQuery}'. Essayez avec d'autres mots-clés.`"
        @load-more="loadMore"
        @retry="handleSearch"
      />
    </div>

    <!-- Categories when no search -->
    <div v-else>
      <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Parcourir par catégorie</h2>
        <div class="grid md:grid-cols-3 gap-6">
          <router-link
            to="/popular"
            class="block p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow"
          >
            <div class="text-3xl mb-3">🔥</div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Films populaires</h3>
            <p class="text-gray-600">Les films les plus regardés en ce moment</p>
          </router-link>
          
          <router-link
            to="/top-rated"
            class="block p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow"
          >
            <div class="text-3xl mb-3">⭐</div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Les mieux notés</h3>
            <p class="text-gray-600">Films avec les meilleures critiques</p>
          </router-link>
          
          <button
            @click="loadNowPlaying"
            class="block w-full text-left p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow"
          >
            <div class="text-3xl mb-3">🎬</div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Au cinéma</h3>
            <p class="text-gray-600">Films actuellement en salle</p>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { MagnifyingGlassIcon } from '@heroicons/vue/24/outline'
import { useMoviesStore } from '@/stores/movies'
import MoviesGrid from '@/components/MoviesGrid.vue'

const route = useRoute()
const router = useRouter()
const moviesStore = useMoviesStore()

const searchQuery = ref('')
const currentQuery = ref('')
const currentPage = ref(1)
const isLoadingMore = ref(false)

const canLoadMore = computed(() => {
  return currentPage.value < moviesStore.totalPages && moviesStore.searchResults.length > 0
})

const handleSearch = async () => {
  const query = searchQuery.value.trim()
  if (!query) return

  currentQuery.value = query
  currentPage.value = 1
  
  try {
    await moviesStore.searchMovies(query, 1)
    // Mettre à jour l'URL
    router.push(`/search?q=${encodeURIComponent(query)}`)
  } catch (error) {
    console.error('Erreur lors de la recherche:', error)
  }
}

const searchFromHistory = (query) => {
  searchQuery.value = query
  handleSearch()
}

const loadMore = async () => {
  if (isLoadingMore.value || !canLoadMore.value || !currentQuery.value) return
  
  isLoadingMore.value = true
  try {
    const nextPage = currentPage.value + 1
    await moviesStore.searchMovies(currentQuery.value, nextPage)
    currentPage.value = nextPage
  } catch (error) {
    console.error('Erreur lors du chargement de plus de résultats:', error)
  } finally {
    isLoadingMore.value = false
  }
}

const loadNowPlaying = async () => {
  try {
    await moviesStore.loadNowPlayingMovies()
    // Afficher les résultats comme une recherche
    currentQuery.value = 'Films au cinéma'
    moviesStore.searchResults = moviesStore.nowPlayingMovies
  } catch (error) {
    console.error('Erreur lors du chargement des films au cinéma:', error)
  }
}

// Watcher pour les changements de route
watch(() => route.query.q, (newQuery) => {
  if (newQuery && newQuery !== currentQuery.value) {
    searchQuery.value = newQuery
    currentQuery.value = newQuery
    moviesStore.searchMovies(newQuery, 1)
  } else if (!newQuery) {
    currentQuery.value = ''
    moviesStore.clearSearchResults()
  }
}, { immediate: true })

onMounted(() => {
  // Si on a une query dans l'URL, l'utiliser
  const queryParam = route.query.q
  if (queryParam) {
    searchQuery.value = queryParam
    currentQuery.value = queryParam
    moviesStore.searchMovies(queryParam, 1)
  }
})
</script>
