import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DownloadsController } from './downloads.controller';
import { DownloadsService } from './downloads.service';
import { Download } from '../entities/download.entity';
import { Movie } from '../entities/movie.entity';
import { User } from '../entities/user.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Download, Movie, User])],
  controllers: [DownloadsController],
  providers: [DownloadsService],
  exports: [DownloadsService],
})
export class DownloadsModule {}
