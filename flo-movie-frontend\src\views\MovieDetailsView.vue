<template>
  <div class="container mx-auto px-4 py-8">
    <!-- Loading state -->
    <div v-if="moviesStore.isLoading" class="animate-pulse">
      <div class="flex flex-col lg:flex-row gap-8">
        <div class="lg:w-1/3">
          <div class="bg-gray-300 rounded-lg h-96 w-full"></div>
        </div>
        <div class="lg:w-2/3">
          <div class="h-8 bg-gray-300 rounded mb-4"></div>
          <div class="h-4 bg-gray-300 rounded mb-2"></div>
          <div class="h-4 bg-gray-300 rounded mb-2"></div>
          <div class="h-4 bg-gray-300 rounded w-3/4"></div>
        </div>
      </div>
    </div>

    <!-- Movie details -->
    <div v-else-if="movie" class="flex flex-col lg:flex-row gap-8">
      <!-- Poster -->
      <div class="lg:w-1/3">
        <img
          :src="posterUrl"
          :alt="movie.title"
          class="w-full rounded-lg shadow-lg"
          @error="handleImageError"
        />
      </div>

      <!-- Details -->
      <div class="lg:w-2/3">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">{{ movie.title }}</h1>
        
        <div class="flex items-center gap-4 mb-6">
          <span class="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-medium">
            ⭐ {{ movie.vote_average?.toFixed(1) || 'N/A' }}
          </span>
          <span class="text-gray-600">{{ formatDate(movie.release_date) }}</span>
          <span v-if="movie.runtime" class="text-gray-600">{{ movie.runtime }} min</span>
        </div>

        <!-- Genres -->
        <div v-if="movie.genres && movie.genres.length > 0" class="mb-6">
          <div class="flex flex-wrap gap-2">
            <span
              v-for="genre in movie.genres"
              :key="genre.id"
              class="bg-gray-200 text-gray-800 px-3 py-1 rounded-full text-sm"
            >
              {{ genre.name }}
            </span>
          </div>
        </div>

        <!-- Overview -->
        <div class="mb-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-3">Synopsis</h2>
          <p class="text-gray-700 leading-relaxed">
            {{ movie.overview || 'Aucun synopsis disponible.' }}
          </p>
        </div>

        <!-- Actions -->
        <div class="flex gap-4 mb-6">
          <button
            v-if="authStore.isAuthenticated"
            @click="toggleFavorite"
            :disabled="isToggling"
            class="flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-colors"
            :class="isInFavorites 
              ? 'bg-red-600 hover:bg-red-700 text-white' 
              : 'bg-gray-200 hover:bg-gray-300 text-gray-800'"
          >
            <HeartIcon :class="['h-5 w-5', isInFavorites ? 'fill-current' : '']" />
            {{ isInFavorites ? 'Retirer des favoris' : 'Ajouter aux favoris' }}
          </button>

          <button
            v-if="authStore.isAuthenticated"
            @click="downloadMovie"
            class="flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
          >
            <ArrowDownTrayIcon class="h-5 w-5" />
            Télécharger
          </button>
        </div>

        <!-- Additional info -->
        <div class="grid md:grid-cols-2 gap-6">
          <div>
            <h3 class="font-semibold text-gray-900 mb-2">Informations</h3>
            <dl class="space-y-1 text-sm">
              <div class="flex">
                <dt class="font-medium text-gray-600 w-24">Note :</dt>
                <dd>{{ movie.vote_average?.toFixed(1) || 'N/A' }}/10 ({{ movie.vote_count || 0 }} votes)</dd>
              </div>
              <div class="flex">
                <dt class="font-medium text-gray-600 w-24">Sortie :</dt>
                <dd>{{ formatDate(movie.release_date) }}</dd>
              </div>
              <div v-if="movie.runtime" class="flex">
                <dt class="font-medium text-gray-600 w-24">Durée :</dt>
                <dd>{{ movie.runtime }} minutes</dd>
              </div>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Error state -->
    <div v-else-if="moviesStore.error" class="text-center py-12">
      <div class="text-red-400 text-6xl mb-4">⚠️</div>
      <h3 class="text-xl font-semibold text-gray-900 mb-2">Erreur de chargement</h3>
      <p class="text-gray-600 mb-4">{{ moviesStore.error }}</p>
      <button @click="loadMovie" class="btn-primary">
        Réessayer
      </button>
    </div>

    <!-- Not found -->
    <div v-else class="text-center py-12">
      <div class="text-gray-400 text-6xl mb-4">🎬</div>
      <h3 class="text-xl font-semibold text-gray-900 mb-2">Film non trouvé</h3>
      <p class="text-gray-600">Ce film n'existe pas ou n'est plus disponible.</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { HeartIcon, ArrowDownTrayIcon } from '@heroicons/vue/24/outline'
import { useAuthStore } from '@/stores/auth'
import { useMoviesStore } from '@/stores/movies'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const moviesStore = useMoviesStore()

const isToggling = ref(false)
const imageError = ref(false)

const movie = computed(() => moviesStore.currentMovie)

const posterUrl = computed(() => {
  if (imageError.value || !movie.value?.poster_path) {
    return 'https://via.placeholder.com/400x600/374151/ffffff?text=No+Image'
  }
  return moviesStore.getMovieImageUrl(movie.value.poster_path, 'w500')
})

const isInFavorites = computed(() => {
  if (!movie.value) return false
  return moviesStore.isMovieInFavorites(movie.value.id)
})

const handleImageError = () => {
  imageError.value = true
}

const formatDate = (dateString) => {
  if (!dateString) return 'Date inconnue'
  const date = new Date(dateString)
  return date.toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const toggleFavorite = async () => {
  if (isToggling.value || !movie.value) return
  
  isToggling.value = true
  
  try {
    const movieData = {
      tmdbId: movie.value.id,
      title: movie.value.title,
      overview: movie.value.overview,
      releaseDate: movie.value.release_date,
      posterPath: movie.value.poster_path,
      backdropPath: movie.value.backdrop_path,
      voteAverage: movie.value.vote_average,
      voteCount: movie.value.vote_count,
      genres: movie.value.genres
    }
    
    if (isInFavorites.value) {
      await moviesStore.removeFromFavorites(movie.value.id)
    } else {
      await moviesStore.addToFavorites(movieData)
    }
  } catch (error) {
    console.error('Erreur lors de la gestion des favoris:', error)
  } finally {
    isToggling.value = false
  }
}

const downloadMovie = () => {
  // Rediriger vers la page de téléchargement
  router.push(`/downloads?movie=${movie.value.id}`)
}

const loadMovie = async () => {
  const movieId = route.params.id
  if (movieId) {
    try {
      await moviesStore.loadMovieDetails(movieId)
    } catch (error) {
      console.error('Erreur lors du chargement du film:', error)
    }
  }
}

onMounted(() => {
  loadMovie()
  // Charger les favoris si l'utilisateur est connecté
  if (authStore.isAuthenticated) {
    moviesStore.loadFavorites()
  }
})
</script>
