import { IsString, IsOptional, <PERSON>N<PERSON>ber, IsArray } from 'class-validator';

export class SearchMovieDto {
  @IsString()
  query: string;

  @IsOptional()
  @IsNumber()
  page?: number = 1;
}

export class MovieResponseDto {
  id: number;
  tmdbId: number;
  title: string;
  overview?: string;
  releaseDate?: string;
  posterPath?: string;
  backdropPath?: string;
  voteAverage?: number;
  voteCount?: number;
  genres?: any[];
  runtime?: number;
}

export class AddFavoriteDto {
  @IsNumber()
  tmdbId: number;

  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  overview?: string;

  @IsOptional()
  @IsString()
  releaseDate?: string;

  @IsOptional()
  @IsString()
  posterPath?: string;

  @IsOptional()
  @IsString()
  backdropPath?: string;

  @IsOptional()
  @IsNumber()
  voteAverage?: number;

  @IsOptional()
  @IsNumber()
  voteCount?: number;

  @IsOptional()
  @IsArray()
  genres?: any[];

  @IsOptional()
  @IsNumber()
  runtime?: number;
}
