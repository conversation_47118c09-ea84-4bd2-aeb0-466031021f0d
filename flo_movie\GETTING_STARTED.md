# Guide de démarrage rapide - Flo Movie API

## 🚀 Démarrage rapide

### 1. Configuration initiale

```bash
# C<PERSON>r le projet (si nécessaire)
cd flo_movie

# Installer les dépendances
npm install

# Configurer l'environnement
cp .env.example .env
```

### 2. Configuration de l'API TMDB

1. Créez un compte gratuit sur [TMDB](https://www.themoviedb.org/)
2. Allez dans Paramètres → API
3. Demandez une clé API
4. Copiez votre clé dans le fichier `.env` :

```env
TMDB_API_KEY=votre_cle_api_ici
```

### 3. Démarrer l'application

```bash
# Mode développement
npm run start:dev

# L'API sera accessible sur http://localhost:3000
```

## 🧪 Tests rapides

### Test 1 : Vérifier que l'API fonctionne
```bash
curl http://localhost:3000
# Réponse attendue : "Hello World!"
```

### Test 2 : Créer un compte utilisateur
```bash
curl -X POST http://localhost:3000/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "testuser",
    "password": "password123",
    "firstName": "John",
    "lastName": "Doe"
  }'
```

### Test 3 : Se connecter
```bash
curl -X POST http://localhost:3000/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

**Important** : Copiez le token JWT retourné pour les tests suivants.

### Test 4 : Rechercher des films (nécessite une clé TMDB)
```bash
curl "http://localhost:3000/movies/search?query=avengers&page=1"
```

### Test 5 : Ajouter un film aux favoris
```bash
curl -X POST http://localhost:3000/movies/favorites \
  -H "Authorization: Bearer VOTRE_TOKEN_JWT" \
  -H "Content-Type: application/json" \
  -d '{
    "tmdbId": 299536,
    "title": "Avengers: Infinity War",
    "overview": "Description du film...",
    "releaseDate": "2018-04-25",
    "posterPath": "/7WsyChQLEftFiDOVTGkv3hFpyyt.jpg",
    "voteAverage": 8.3
  }'
```

### Test 6 : Lister les favoris
```bash
curl -H "Authorization: Bearer VOTRE_TOKEN_JWT" \
  http://localhost:3000/movies/favorites/list
```

## 📊 Exécuter les tests unitaires

```bash
# Tous les tests
npm test

# Tests spécifiques
npm test -- --testPathPatterns=auth.service.spec.ts
npm test -- --testPathPatterns=movies.service.spec.ts

# Tests avec couverture
npm run test:cov
```

## 🗄️ Base de données

L'application utilise SQLite par défaut. Le fichier `flo_movie.db` sera créé automatiquement au premier démarrage.

Pour voir les données :
- Utilisez un outil comme DB Browser for SQLite
- Ou connectez-vous via la ligne de commande SQLite

## 📝 Endpoints disponibles

### Authentification
- `POST /auth/register` - Inscription
- `POST /auth/login` - Connexion

### Films (publics)
- `GET /movies/search?query=...&page=1` - Recherche
- `GET /movies/popular?page=1` - Films populaires
- `GET /movies/top-rated?page=1` - Films les mieux notés
- `GET /movies/now-playing?page=1` - Films au cinéma
- `GET /movies/:id` - Détails d'un film

### Favoris (authentification requise)
- `POST /movies/favorites` - Ajouter aux favoris
- `GET /movies/favorites/list` - Lister les favoris
- `GET /movies/favorites/check/:tmdbId` - Vérifier si en favoris
- `DELETE /movies/favorites/:tmdbId` - Supprimer des favoris

### Téléchargements (authentification requise)
- `POST /downloads` - Créer un téléchargement
- `GET /downloads` - Lister les téléchargements
- `GET /downloads/stats` - Statistiques
- `POST /downloads/:id/start` - Démarrer
- `POST /downloads/:id/simulate` - Simuler (pour tests)
- `PUT /downloads/:id/progress` - Mettre à jour la progression
- `DELETE /downloads/:id` - Annuler

## 🔧 Dépannage

### Erreur "TMDB API"
- Vérifiez que votre clé API TMDB est correcte dans `.env`
- Vérifiez votre connexion internet

### Erreur de base de données
- Supprimez le fichier `flo_movie.db` et redémarrez l'application
- Vérifiez les permissions du dossier

### Tests qui échouent
- Assurez-vous que l'application n'est pas en cours d'exécution
- Exécutez `npm install` pour réinstaller les dépendances

## 🎯 Prochaines étapes

1. **Frontend** : Créez une interface web avec React, Vue.js ou Angular
2. **Déploiement** : Déployez sur Heroku, Vercel ou AWS
3. **Base de données** : Migrez vers PostgreSQL pour la production
4. **Cache** : Ajoutez Redis pour améliorer les performances
5. **Notifications** : Implémentez WebSockets pour les notifications temps réel

## 📞 Support

Si vous rencontrez des problèmes :
1. Vérifiez les logs de l'application
2. Consultez la documentation complète dans `README.md`
3. Vérifiez que toutes les dépendances sont installées
4. Assurez-vous que le port 3000 est libre

Bon développement ! 🎬
