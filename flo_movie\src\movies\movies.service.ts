import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Movie } from '../entities/movie.entity';
import { Favorite } from '../entities/favorite.entity';
import { User } from '../entities/user.entity';
import { TmdbService } from './tmdb.service';
import { AddFavoriteDto } from '../dto/movie.dto';

@Injectable()
export class MoviesService {
  constructor(
    @InjectRepository(Movie)
    private movieRepository: Repository<Movie>,
    @InjectRepository(Favorite)
    private favoriteRepository: Repository<Favorite>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private tmdbService: TmdbService,
  ) {}

  async searchMovies(query: string, page: number = 1) {
    return this.tmdbService.searchMovies(query, page);
  }

  async getMovieDetails(tmdbId: number) {
    return this.tmdbService.getMovieDetails(tmdbId);
  }

  async getPopularMovies(page: number = 1) {
    return this.tmdbService.getPopularMovies(page);
  }

  async getTopRatedMovies(page: number = 1) {
    return this.tmdbService.getTopRatedMovies(page);
  }

  async getNowPlayingMovies(page: number = 1) {
    return this.tmdbService.getNowPlayingMovies(page);
  }

  async addToFavorites(userId: number, addFavoriteDto: AddFavoriteDto): Promise<Favorite> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('Utilisateur non trouvé');
    }

    // Vérifier si le film existe déjà dans notre base
    let movie = await this.movieRepository.findOne({ where: { tmdbId: addFavoriteDto.tmdbId } });

    // Si le film n'existe pas, le créer
    if (!movie) {
      movie = this.movieRepository.create({
        tmdbId: addFavoriteDto.tmdbId,
        title: addFavoriteDto.title,
        overview: addFavoriteDto.overview,
        releaseDate: addFavoriteDto.releaseDate,
        posterPath: addFavoriteDto.posterPath,
        backdropPath: addFavoriteDto.backdropPath,
        voteAverage: addFavoriteDto.voteAverage,
        voteCount: addFavoriteDto.voteCount,
        genres: addFavoriteDto.genres ? JSON.stringify(addFavoriteDto.genres) : undefined,
        runtime: addFavoriteDto.runtime,
      });
      movie = await this.movieRepository.save(movie);
    }

    // Vérifier si le film est déjà en favoris
    const existingFavorite = await this.favoriteRepository.findOne({
      where: { user: { id: userId }, movie: { id: movie.id } },
    });

    if (existingFavorite) {
      return existingFavorite;
    }

    // Créer le favori
    const favorite = this.favoriteRepository.create({
      user,
      movie,
    });

    return this.favoriteRepository.save(favorite);
  }

  async removeFromFavorites(userId: number, tmdbId: number): Promise<void> {
    const movie = await this.movieRepository.findOne({ where: { tmdbId } });
    if (!movie) {
      throw new NotFoundException('Film non trouvé');
    }

    const favorite = await this.favoriteRepository.findOne({
      where: { user: { id: userId }, movie: { id: movie.id } },
    });

    if (!favorite) {
      throw new NotFoundException('Ce film n\'est pas dans vos favoris');
    }

    await this.favoriteRepository.remove(favorite);
  }

  async getUserFavorites(userId: number): Promise<Favorite[]> {
    return this.favoriteRepository.find({
      where: { user: { id: userId } },
      relations: ['movie'],
      order: { createdAt: 'DESC' },
    });
  }

  async isMovieInFavorites(userId: number, tmdbId: number): Promise<boolean> {
    const movie = await this.movieRepository.findOne({ where: { tmdbId } });
    if (!movie) {
      return false;
    }

    const favorite = await this.favoriteRepository.findOne({
      where: { user: { id: userId }, movie: { id: movie.id } },
    });

    return !!favorite;
  }
}
