import { defineStore } from 'pinia'
import { authService } from '@/services/auth'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    token: null,
    isLoading: false,
    error: null
  }),

  getters: {
    isAuthenticated: (state) => !!state.token,
    currentUser: (state) => state.user
  },

  actions: {
    // Initialiser le store avec les données du localStorage
    initializeAuth() {
      const token = authService.getToken()
      const user = authService.getCurrentUser()
      
      if (token && user) {
        this.token = token
        this.user = user
      }
    },

    // Inscription
    async register(userData) {
      this.isLoading = true
      this.error = null

      try {
        const response = await authService.register(userData)
        this.user = response.user
        this.token = response.token
        return response
      } catch (error) {
        this.error = error.message || 'Erreur lors de l\'inscription'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    // Connexion
    async login(credentials) {
      this.isLoading = true
      this.error = null

      try {
        const response = await authService.login(credentials)
        this.user = response.user
        this.token = response.token
        return response
      } catch (error) {
        this.error = error.message || 'Erreur lors de la connexion'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    // Déconnexion
    logout() {
      authService.logout()
      this.user = null
      this.token = null
      this.error = null
    },

    // Effacer les erreurs
    clearError() {
      this.error = null
    }
  }
})
