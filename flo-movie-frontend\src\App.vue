<script setup>
import { onMounted } from 'vue'
import { RouterView } from 'vue-router'
import NavBar from './components/NavBar.vue'
import { useAuthStore } from './stores/auth'

const authStore = useAuthStore()

onMounted(() => {
  // Initialiser l'authentification au démarrage de l'app
  authStore.initializeAuth()
})
</script>

<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <NavBar />
    <main>
      <RouterView />
    </main>
  </div>
</template>

<style>
/* Styles globaux déjà définis dans main.css */
</style>
