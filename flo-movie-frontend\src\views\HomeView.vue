<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
    <!-- Hero Section -->
    <div class="relative overflow-hidden">
      <!-- Background decoration -->
      <div class="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10"></div>
      <div class="absolute top-0 left-0 w-full h-full">
        <div class="absolute top-20 left-10 w-32 h-32 bg-yellow-300/20 rounded-full blur-xl"></div>
        <div class="absolute top-40 right-20 w-24 h-24 bg-blue-400/20 rounded-full blur-lg"></div>
        <div class="absolute bottom-20 left-1/4 w-40 h-40 bg-purple-300/20 rounded-full blur-2xl"></div>
      </div>

      <div class="relative container mx-auto px-4 py-16 text-center">
        <div class="mb-8">
          <!-- Logo avec animation -->
          <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl mb-6 shadow-2xl transform hover:scale-110 transition-transform duration-300">
            <span class="text-3xl">🎬</span>
          </div>
          <h1 class="text-5xl md:text-7xl font-black bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-4">
            Flo Movie
          </h1>
          <p class="text-xl md:text-2xl text-gray-700 mb-8 max-w-3xl mx-auto leading-relaxed">
            Découvrez un univers cinématographique infini. Recherchez, explorez et organisez votre collection de films préférés.
          </p>
        </div>

        <!-- Search bar améliorée -->
        <div class="max-w-2xl mx-auto mb-12">
          <div class="relative group">
            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
              <MagnifyingGlassIcon class="h-6 w-6 text-gray-400 group-focus-within:text-blue-500 transition-colors" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              class="block w-full pl-12 pr-4 py-4 border-2 border-gray-200 rounded-2xl leading-5 bg-white/80 backdrop-blur-sm placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 text-lg shadow-xl transition-all duration-300"
              placeholder="Rechercher un film, un acteur, un réalisateur..."
              @keyup.enter="handleSearch"
            />
            <div class="absolute inset-y-0 right-0 pr-2 flex items-center">
              <button
                @click="handleSearch"
                class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg"
              >
                Rechercher
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Categories avec design moderne -->
    <div class="container mx-auto px-4 mb-16">
      <div class="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
        <router-link
          to="/popular"
          class="group relative overflow-hidden bg-gradient-to-br from-red-500 to-pink-600 text-white p-8 rounded-2xl shadow-xl hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300"
        >
          <div class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
          <div class="relative z-10">
            <div class="text-4xl mb-4">🔥</div>
            <h3 class="text-xl font-bold mb-2">Films populaires</h3>
            <p class="text-red-100 text-sm">Les plus regardés en ce moment</p>
          </div>
        </router-link>

        <router-link
          to="/top-rated"
          class="group relative overflow-hidden bg-gradient-to-br from-yellow-500 to-orange-600 text-white p-8 rounded-2xl shadow-xl hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300"
        >
          <div class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
          <div class="relative z-10">
            <div class="text-4xl mb-4">⭐</div>
            <h3 class="text-xl font-bold mb-2">Les mieux notés</h3>
            <p class="text-yellow-100 text-sm">Films avec les meilleures critiques</p>
          </div>
        </router-link>

        <button
          @click="loadNowPlaying"
          class="group relative overflow-hidden bg-gradient-to-br from-green-500 to-emerald-600 text-white p-8 rounded-2xl shadow-xl hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300"
        >
          <div class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
          <div class="relative z-10">
            <div class="text-4xl mb-4">🎬</div>
            <h3 class="text-xl font-bold mb-2">Au cinéma</h3>
            <p class="text-green-100 text-sm">Films actuellement en salle</p>
          </div>
        </button>
      </div>
    </div>

    <!-- Popular Movies Preview avec design amélioré -->
    <div class="container mx-auto px-4 mb-16">
      <div class="flex justify-between items-center mb-8">
        <div>
          <h2 class="text-3xl font-bold text-gray-900 mb-2">Films populaires</h2>
          <p class="text-gray-600">Découvrez les films les plus appréciés du moment</p>
        </div>
        <router-link
          to="/popular"
          class="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl font-medium transition-colors shadow-lg"
        >
          Voir tout
          <svg class="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </router-link>
      </div>

      <MoviesGrid
        :movies="popularMovies.slice(0, 10)"
        :is-loading="moviesStore.isLoading"
        :error="moviesStore.error"
        empty-message="Aucun film populaire disponible"
        empty-description="Vérifiez votre connexion internet et réessayez."
        @retry="loadPopularMovies"
      />
    </div>

    <!-- Features avec design moderne -->
    <div class="container mx-auto px-4 mb-16">
      <div class="text-center mb-12">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">Pourquoi choisir Flo Movie ?</h2>
        <p class="text-gray-600 max-w-2xl mx-auto">Une expérience cinématographique complète avec des fonctionnalités avancées pour tous les amateurs de films.</p>
      </div>

      <div class="grid md:grid-cols-3 gap-8">
        <div class="group bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
          <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
            <span class="text-2xl">🔍</span>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-3">Recherche intelligente</h3>
          <p class="text-gray-600 leading-relaxed">Moteur de recherche avancé avec suggestions automatiques et filtres personnalisés pour trouver exactement ce que vous cherchez.</p>
        </div>

        <div class="group bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
          <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
            <span class="text-2xl">⭐</span>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-3">Collection personnelle</h3>
          <p class="text-gray-600 leading-relaxed">Créez et organisez votre bibliothèque de films favoris avec des listes personnalisées et des recommandations intelligentes.</p>
        </div>

        <div class="group bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
          <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
            <span class="text-2xl">📥</span>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-3">Téléchargements HD</h3>
          <p class="text-gray-600 leading-relaxed">Téléchargez vos films en haute qualité avec un gestionnaire intégré et un suivi de progression en temps réel.</p>
        </div>
      </div>
    </div>

    <!-- Call to action -->
    <div class="container mx-auto px-4 text-center">
      <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-12 text-white">
        <h2 class="text-3xl font-bold mb-4">Prêt à explorer ?</h2>
        <p class="text-blue-100 mb-8 max-w-2xl mx-auto">Rejoignez des milliers d'utilisateurs qui ont déjà découvert leur nouvelle façon de regarder des films.</p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <router-link
            v-if="!authStore.isAuthenticated"
            to="/register"
            class="bg-white text-blue-600 px-8 py-3 rounded-xl font-bold hover:bg-gray-100 transition-colors shadow-lg"
          >
            Créer un compte gratuit
          </router-link>
          <router-link
            to="/popular"
            class="bg-blue-500/20 backdrop-blur-sm text-white px-8 py-3 rounded-xl font-medium hover:bg-blue-500/30 transition-colors border border-blue-400/30"
          >
            Explorer les films
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { MagnifyingGlassIcon } from '@heroicons/vue/24/outline'
import { useMoviesStore } from '@/stores/movies'
import MoviesGrid from '@/components/MoviesGrid.vue'

const router = useRouter()
const moviesStore = useMoviesStore()

const searchQuery = ref('')

const popularMovies = computed(() => moviesStore.popularMovies)

const handleSearch = () => {
  if (searchQuery.value.trim()) {
    router.push(`/search?q=${encodeURIComponent(searchQuery.value.trim())}`)
  }
}

const loadPopularMovies = async () => {
  try {
    await moviesStore.loadPopularMovies()
  } catch (error) {
    console.error('Erreur lors du chargement des films populaires:', error)
  }
}

const loadNowPlaying = async () => {
  try {
    await moviesStore.loadNowPlayingMovies()
    router.push('/search?category=now-playing')
  } catch (error) {
    console.error('Erreur lors du chargement des films au cinéma:', error)
  }
}

onMounted(() => {
  loadPopularMovies()
})
</script>
