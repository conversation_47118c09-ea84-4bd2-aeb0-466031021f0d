import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class TmdbService {
  private readonly apiKey: string;
  private readonly baseUrl: string;

  constructor(private configService: ConfigService) {
    this.apiKey = this.configService.get<string>('TMDB_API_KEY') || '';
    this.baseUrl = this.configService.get<string>('TMDB_BASE_URL', 'https://api.themoviedb.org/3');
  }

  async searchMovies(query: string, page: number = 1): Promise<any> {
    try {
      const response = await fetch(
        `${this.baseUrl}/search/movie?api_key=${this.apiKey}&query=${encodeURIComponent(query)}&page=${page}&language=fr-FR`
      );

      if (!response.ok) {
        throw new HttpException('Erreur lors de la recherche de films', HttpStatus.BAD_REQUEST);
      }

      return await response.json();
    } catch (error) {
      throw new HttpException('Erreur lors de la recherche de films', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getMovieDetails(movieId: number): Promise<any> {
    try {
      const response = await fetch(
        `${this.baseUrl}/movie/${movieId}?api_key=${this.apiKey}&language=fr-FR&append_to_response=credits,videos`
      );

      if (!response.ok) {
        throw new HttpException('Film non trouvé', HttpStatus.NOT_FOUND);
      }

      return await response.json();
    } catch (error) {
      throw new HttpException('Erreur lors de la récupération des détails du film', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getPopularMovies(page: number = 1): Promise<any> {
    try {
      const response = await fetch(
        `${this.baseUrl}/movie/popular?api_key=${this.apiKey}&page=${page}&language=fr-FR`
      );

      if (!response.ok) {
        throw new HttpException('Erreur lors de la récupération des films populaires', HttpStatus.BAD_REQUEST);
      }

      return await response.json();
    } catch (error) {
      throw new HttpException('Erreur lors de la récupération des films populaires', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getTopRatedMovies(page: number = 1): Promise<any> {
    try {
      const response = await fetch(
        `${this.baseUrl}/movie/top_rated?api_key=${this.apiKey}&page=${page}&language=fr-FR`
      );

      if (!response.ok) {
        throw new HttpException('Erreur lors de la récupération des films les mieux notés', HttpStatus.BAD_REQUEST);
      }

      return await response.json();
    } catch (error) {
      throw new HttpException('Erreur lors de la récupération des films les mieux notés', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getNowPlayingMovies(page: number = 1): Promise<any> {
    try {
      const response = await fetch(
        `${this.baseUrl}/movie/now_playing?api_key=${this.apiKey}&page=${page}&language=fr-FR`
      );

      if (!response.ok) {
        throw new HttpException('Erreur lors de la récupération des films actuellement au cinéma', HttpStatus.BAD_REQUEST);
      }

      return await response.json();
    } catch (error) {
      throw new HttpException('Erreur lors de la récupération des films actuellement au cinéma', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  getImageUrl(path: string, size: string = 'w500'): string {
    return `https://image.tmdb.org/t/p/${size}${path}`;
  }
}
