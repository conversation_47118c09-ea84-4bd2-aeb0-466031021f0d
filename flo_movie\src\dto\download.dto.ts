import { IsN<PERSON>ber, IsString, IsEnum, IsOptional, IsUrl } from 'class-validator';
import { DownloadQuality } from '../entities/download.entity';

export class CreateDownloadDto {
  @IsNumber()
  tmdbId: number;

  @IsString()
  title: string;

  @IsEnum(DownloadQuality)
  quality: DownloadQuality;

  @IsUrl()
  downloadUrl: string;

  @IsOptional()
  @IsString()
  fileName?: string;

  @IsOptional()
  @IsNumber()
  fileSize?: number;
}

export class UpdateDownloadProgressDto {
  @IsNumber()
  progress: number;

  @IsOptional()
  @IsString()
  errorMessage?: string;
}
