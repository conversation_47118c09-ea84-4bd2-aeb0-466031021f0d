<template>
  <div class="container mx-auto px-4 py-8">
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">Mes favoris</h1>
      <p class="text-gray-600">Retrouvez tous vos films préférés</p>
    </div>

    <!-- Loading state -->
    <div v-if="moviesStore.isLoadingFavorites" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
      <div v-for="n in 8" :key="n" class="animate-pulse">
        <div class="bg-gray-300 rounded-lg h-64 mb-4"></div>
        <div class="h-4 bg-gray-300 rounded mb-2"></div>
        <div class="h-3 bg-gray-300 rounded mb-2"></div>
        <div class="h-3 bg-gray-300 rounded w-3/4"></div>
      </div>
    </div>

    <!-- Favorites grid -->
    <div v-else-if="favorites.length > 0" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
      <div
        v-for="favorite in favorites"
        :key="favorite.id"
        class="movie-card bg-white rounded-lg shadow-md overflow-hidden cursor-pointer"
        @click="goToMovie(favorite.movie.tmdbId)"
      >
        <div class="relative">
          <img
            :src="getMovieImageUrl(favorite.movie.posterPath)"
            :alt="favorite.movie.title"
            class="w-full h-64 object-cover"
            @error="(e) => handleImageError(e, favorite.movie.posterPath)"
          />
          <div class="absolute top-2 right-2">
            <button
              @click.stop="removeFromFavorites(favorite.movie.tmdbId)"
              class="p-2 rounded-full bg-black bg-opacity-50 hover:bg-opacity-70 transition-all"
              :disabled="isRemoving"
            >
              <HeartIcon class="h-5 w-5 text-red-500 fill-current" />
            </button>
          </div>
          <div class="absolute bottom-2 left-2">
            <span class="bg-black bg-opacity-70 text-white px-2 py-1 rounded text-sm">
              ⭐ {{ favorite.movie.voteAverage?.toFixed(1) || 'N/A' }}
            </span>
          </div>
        </div>
        
        <div class="p-4">
          <h3 class="font-semibold text-lg text-gray-900 mb-2 line-clamp-2">
            {{ favorite.movie.title }}
          </h3>
          <p class="text-gray-600 text-sm mb-2">
            {{ formatDate(favorite.movie.releaseDate) }}
          </p>
          <p class="text-gray-700 text-sm line-clamp-3">
            {{ favorite.movie.overview }}
          </p>
          <p class="text-gray-500 text-xs mt-2">
            Ajouté le {{ formatDate(favorite.createdAt) }}
          </p>
        </div>
      </div>
    </div>

    <!-- Empty state -->
    <div v-else-if="!moviesStore.isLoadingFavorites" class="text-center py-12">
      <div class="text-gray-400 text-6xl mb-4">⭐</div>
      <h3 class="text-xl font-semibold text-gray-900 mb-2">Aucun favori</h3>
      <p class="text-gray-600 mb-6">Vous n'avez pas encore ajouté de films à vos favoris.</p>
      <router-link
        to="/popular"
        class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors inline-block"
      >
        Découvrir des films
      </router-link>
    </div>

    <!-- Error state -->
    <div v-if="moviesStore.error" class="text-center py-12">
      <div class="text-red-400 text-6xl mb-4">⚠️</div>
      <h3 class="text-xl font-semibold text-gray-900 mb-2">Erreur de chargement</h3>
      <p class="text-gray-600 mb-4">{{ moviesStore.error }}</p>
      <button @click="loadFavorites" class="btn-primary">
        Réessayer
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { HeartIcon } from '@heroicons/vue/24/outline'
import { useMoviesStore } from '@/stores/movies'

const router = useRouter()
const moviesStore = useMoviesStore()

const isRemoving = ref(false)

const favorites = computed(() => moviesStore.favorites)

const goToMovie = (tmdbId) => {
  router.push(`/movie/${tmdbId}`)
}

const getMovieImageUrl = (path) => {
  if (!path) {
    return 'https://via.placeholder.com/300x450/374151/ffffff?text=No+Image'
  }
  return moviesStore.getMovieImageUrl(path)
}

const handleImageError = (event, originalPath) => {
  event.target.src = 'https://via.placeholder.com/300x450/374151/ffffff?text=No+Image'
}

const formatDate = (dateString) => {
  if (!dateString) return 'Date inconnue'
  const date = new Date(dateString)
  return date.toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const removeFromFavorites = async (tmdbId) => {
  if (isRemoving.value) return
  
  isRemoving.value = true
  
  try {
    await moviesStore.removeFromFavorites(tmdbId)
  } catch (error) {
    console.error('Erreur lors de la suppression des favoris:', error)
  } finally {
    isRemoving.value = false
  }
}

const loadFavorites = async () => {
  try {
    await moviesStore.loadFavorites()
  } catch (error) {
    console.error('Erreur lors du chargement des favoris:', error)
  }
}

onMounted(() => {
  loadFavorites()
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
