import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { User } from './user.entity';
import { Movie } from './movie.entity';

export enum DownloadStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

export enum DownloadQuality {
  HD_720P = '720p',
  FULL_HD_1080P = '1080p',
  UHD_4K = '4k',
  CAM = 'cam',
  DVDRIP = 'dvdrip',
  BLURAY = 'bluray',
}

@Entity('downloads')
export class Download {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => Movie, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'movieId' })
  movie: Movie;

  @Column({
    type: 'text',
    default: DownloadStatus.PENDING,
  })
  status: DownloadStatus;

  @Column({
    type: 'text',
    default: DownloadQuality.HD_720P,
  })
  quality: DownloadQuality;

  @Column({ type: 'text' })
  downloadUrl: string;

  @Column({ nullable: true })
  fileName?: string;

  @Column({ type: 'bigint', nullable: true })
  fileSize?: number; // en bytes

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  progress: number; // pourcentage de téléchargement

  @Column({ type: 'text', nullable: true })
  errorMessage?: string;

  @CreateDateColumn()
  createdAt: Date;

  @Column({ nullable: true })
  completedAt?: Date;
}
